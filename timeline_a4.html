<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文艺广播技术搬迁工作时间轴 - A4版</title>
    <style>
        @page {
            size: A4;
            margin: 15mm;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: white;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
        }
        
        .container {
            max-width: 100%;
            margin: 0 auto;
            padding: 10px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        
        .header h1 {
            color: #2c3e50;
            margin: 0 0 5px 0;
            font-size: 20px;
            font-weight: bold;
        }
        
        .header .subtitle {
            color: #7f8c8d;
            font-size: 14px;
            margin: 0;
        }
        
        .objective {
            background: #f8f9fa;
            padding: 10px;
            border-left: 4px solid #3498db;
            margin-bottom: 15px;
            border-radius: 3px;
        }
        
        .objective h2 {
            margin: 0 0 5px 0;
            font-size: 14px;
            color: #2c3e50;
        }
        
        .objective p {
            margin: 0;
            font-size: 12px;
            font-weight: 500;
        }
        
        .timeline {
            position: relative;
            padding: 10px 0;
        }
        
        .timeline::before {
            content: '';
            position: absolute;
            left: 80px;
            top: 0;
            bottom: 0;
            width: 3px;
            background: linear-gradient(to bottom, #3498db, #e74c3c);
        }
        
        .timeline-item {
            position: relative;
            margin: 8px 0;
            padding-left: 100px;
            min-height: 40px;
        }
        
        .timeline-date {
            position: absolute;
            left: 0;
            top: 0;
            width: 75px;
            background: #3498db;
            color: white;
            padding: 4px 6px;
            border-radius: 12px;
            font-weight: bold;
            font-size: 10px;
            text-align: center;
        }
        
        .timeline-item.milestone .timeline-date {
            background: #e74c3c;
        }
        
        .timeline-content {
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 8px 10px;
            margin-left: 10px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .timeline-item.milestone .timeline-content {
            border-left: 4px solid #e74c3c;
            background: #fff5f5;
        }
        
        .timeline-title {
            font-size: 13px;
            font-weight: bold;
            color: #2c3e50;
            margin: 0 0 3px 0;
        }
        
        .timeline-description {
            font-size: 11px;
            color: #555;
            margin: 0;
            line-height: 1.3;
        }
        
        .timeline-description strong {
            color: #2c3e50;
            font-weight: 600;
        }
        
        .timeline-description em {
            color: #e74c3c;
            font-style: normal;
            font-weight: 500;
        }
        
        .timeline-item::before {
            content: '';
            position: absolute;
            left: 71px;
            top: 6px;
            width: 12px;
            height: 12px;
            background: #3498db;
            border: 2px solid white;
            border-radius: 50%;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
        }
        
        .timeline-item.milestone::before {
            background: #e74c3c;
            width: 14px;
            height: 14px;
            top: 5px;
            left: 70px;
        }
        
        .phases {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 15px;
        }
        
        .phase {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
        }
        
        .phase h3 {
            margin: 0 0 8px 0;
            font-size: 13px;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 3px;
        }
        
        .phase ul {
            margin: 0;
            padding-left: 15px;
            font-size: 11px;
        }
        
        .phase li {
            margin-bottom: 3px;
            line-height: 1.3;
        }
        
        .legend {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 10px;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 5px;
            font-size: 10px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }
        
        .legend-blue { background: #3498db; }
        .legend-red { background: #e74c3c; }
        
        @media print {
            body { 
                font-size: 11px; 
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            .container { padding: 0; }
            .timeline-item { margin: 6px 0; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>文艺广播技术搬迁工作时间轴</h1>
            <div class="subtitle">2024年8月1日-9月22日</div>
        </div>
        
        <div class="objective">
            <h2>🎯 项目目标</h2>
            <p>确保文艺广播直播间于9月22日前完成搬迁至梅江10层演播室，并具备正式直播条件</p>
        </div>
        
        <div class="timeline">
            <!-- 8月31日前 - 装修完成 -->
            <div class="timeline-item milestone">
                <div class="timeline-date">8月31日前</div>
                <div class="timeline-content">
                    <div class="timeline-title">🏗️ 装修施工完成</div>
                    <div class="timeline-description">
                        <strong>完成10层演播室及导播间装修</strong><br>
                        含灯光、空调修复、门禁调整、安防监控设备安装
                    </div>
                </div>
            </div>
            
            <!-- 8月31日前 - 技术布线 -->
            <div class="timeline-item">
                <div class="timeline-date">8月31日前</div>
                <div class="timeline-content">
                    <div class="timeline-title">🔌 技术系统布线</div>
                    <div class="timeline-description">
                        <strong>布线施工：</strong>10楼机房、5楼、10楼小语录室<br>
                        <strong>设备安装：</strong>演播室机房设备安装调试
                    </div>
                </div>
            </div>
            
            <!-- 9月1日-20日 -->
            <div class="timeline-item">
                <div class="timeline-date">9月1-20日</div>
                <div class="timeline-content">
                    <div class="timeline-title">📦 基础设备搬迁调试</div>
                    <div class="timeline-description">
                        <strong>直播间及导播间基础设备搬迁调试</strong><br>
                        调通所有路由及信号
                    </div>
                </div>
            </div>
            
            <!-- 9月10日前 -->
            <div class="timeline-item">
                <div class="timeline-date">9月10日前</div>
                <div class="timeline-content">
                    <div class="timeline-title">🚚 先行设备迁移</div>
                    <div class="timeline-description">
                        <em>先行迁移1台音频工作站及1套小语录设备至10楼</em><br>
                        （不影响卫津路正常播出）
                    </div>
                </div>
            </div>
            
            <!-- 9月20日前 -->
            <div class="timeline-item">
                <div class="timeline-date">9月20日前</div>
                <div class="timeline-content">
                    <div class="timeline-title">📋 全部设备迁移</div>
                    <div class="timeline-description">
                        <strong>完成全部设备迁移</strong><br>
                        含语录室、办公区电脑及设备
                    </div>
                </div>
            </div>
            
            <!-- 9月20日-21日 -->
            <div class="timeline-item milestone">
                <div class="timeline-date">9月20-21日</div>
                <div class="timeline-content">
                    <div class="timeline-title">⚠️ 核心设备搬迁</div>
                    <div class="timeline-description">
                        <strong>周末录播期施工：</strong><br>
                        音频工作站、调音台、新媒体直播设备、播出站（小说广播）等
                    </div>
                </div>
            </div>
            
            <!-- 9月22日 -->
            <div class="timeline-item milestone">
                <div class="timeline-date">9月22日</div>
                <div class="timeline-content">
                    <div class="timeline-title">🎉 正式直播启动</div>
                    <div class="timeline-description">
                        <strong>梅江10层演播室首次正式直播</strong><br>
                        <em>技术系统全程保障</em>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="phases">
            <div class="phase">
                <h3>🏗️ 装修施工阶段</h3>
                <ul>
                    <li>10层演播室装修</li>
                    <li>导播间装修</li>
                    <li>灯光系统安装</li>
                    <li>空调修复</li>
                    <li>门禁调整</li>
                    <li>安防监控设备</li>
                </ul>
            </div>
            
            <div class="phase">
                <h3>🔧 技术实施阶段</h3>
                <ul>
                    <li>机房布线工程</li>
                    <li>设备安装调试</li>
                    <li>信号路由调通</li>
                    <li>分阶段设备搬迁</li>
                    <li>核心设备迁移</li>
                    <li>系统联调测试</li>
                </ul>
            </div>
        </div>
        
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color legend-blue"></div>
                <span>常规任务节点</span>
            </div>
            <div class="legend-item">
                <div class="legend-color legend-red"></div>
                <span>重要里程碑</span>
            </div>
        </div>
    </div>
</body>
</html>
