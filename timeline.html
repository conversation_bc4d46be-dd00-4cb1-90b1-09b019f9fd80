<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文艺广播搬迁工作时间轴</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            padding: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .header .subtitle {
            color: #7f8c8d;
            font-size: 16px;
        }
        .timeline {
            position: relative;
            padding: 20px 0;
        }
        .timeline::before {
            content: '';
            position: absolute;
            left: 50%;
            top: 0;
            bottom: 0;
            width: 4px;
            background: linear-gradient(to bottom, #3498db, #e74c3c);
            transform: translateX(-50%);
        }
        .timeline-item {
            position: relative;
            margin: 30px 0;
            width: 45%;
        }
        .timeline-item.left {
            left: 0;
            text-align: right;
        }
        .timeline-item.right {
            left: 55%;
            text-align: left;
        }
        .timeline-content {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #3498db;
            position: relative;
        }
        .timeline-item.right .timeline-content {
            border-left: none;
            border-right: 4px solid #3498db;
        }
        .timeline-date {
            background: #3498db;
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 10px;
            font-size: 14px;
        }
        .timeline-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .timeline-description {
            color: #555;
            line-height: 1.6;
        }
        .timeline-item::before {
            content: '';
            position: absolute;
            top: 30px;
            width: 16px;
            height: 16px;
            background: #3498db;
            border: 4px solid white;
            border-radius: 50%;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        .timeline-item.left::before {
            right: -58px;
        }
        .timeline-item.right::before {
            left: -58px;
        }
        .milestone {
            border-left-color: #e74c3c !important;
        }
        .timeline-item.right .milestone {
            border-right-color: #e74c3c !important;
        }
        .milestone .timeline-date {
            background: #e74c3c;
        }
        .milestone::before {
            background: #e74c3c !important;
        }
        .status-legend {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-top: 40px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 50%;
        }
        .legend-blue { background: #3498db; }
        .legend-red { background: #e74c3c; }
        
        @media (max-width: 768px) {
            .timeline::before {
                left: 30px;
            }
            .timeline-item {
                width: calc(100% - 60px);
                left: 60px !important;
                text-align: left !important;
            }
            .timeline-item::before {
                left: -38px !important;
            }
            .timeline-content {
                border-left: 4px solid #3498db !important;
                border-right: none !important;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>文艺广播搬迁工作时间轴</h1>
            <div class="subtitle">2024年8月-9月 技术工作计划</div>
        </div>
        
        <div class="timeline">
            <!-- 8月4日前 -->
            <div class="timeline-item left">
                <div class="timeline-content">
                    <div class="timeline-date">8月4日前</div>
                    <div class="timeline-title">装修设计优化完成</div>
                    <div class="timeline-description">
                        完成装修设计优化，提交正式3D效果图（演播室、导播间）
                    </div>
                </div>
            </div>
            
            <!-- 8月10日前 -->
            <div class="timeline-item right">
                <div class="timeline-content">
                    <div class="timeline-date">8月10日前</div>
                    <div class="timeline-title">设计定稿与预算确认</div>
                    <div class="timeline-description">
                        确认最终设计稿，提交预算后启动施工
                    </div>
                </div>
            </div>
            
            <!-- 8月15日-31日 -->
            <div class="timeline-item left">
                <div class="timeline-content">
                    <div class="timeline-date">8月15日-31日</div>
                    <div class="timeline-title">并行施工阶段</div>
                    <div class="timeline-description">
                        • 布线施工（10楼机房、5楼、10楼小语录室）<br>
                        • 演播室机房设备安装调试<br>
                        • 与装修同步进行，节约工期
                    </div>
                </div>
            </div>
            
            <!-- 8月31日前 -->
            <div class="timeline-item right milestone">
                <div class="timeline-content milestone">
                    <div class="timeline-date">8月31日前</div>
                    <div class="timeline-title">装修施工完成</div>
                    <div class="timeline-description">
                        完成10层演播室及导播间装修（含灯光、空调修复、门禁调整、安防监控设备安装等）<br>
                        <strong>同时完成：3楼备播间改造</strong>
                    </div>
                </div>
            </div>
            
            <!-- 9月1日-20日 -->
            <div class="timeline-item left">
                <div class="timeline-content">
                    <div class="timeline-date">9月1日-20日</div>
                    <div class="timeline-title">基础设备搬迁调试</div>
                    <div class="timeline-description">
                        • 直播间及导播间基础设备搬迁调试<br>
                        • 需调通所有路由及信号<br>
                        • 9月10日前：先行迁移1台音频工作站及1套小语录设备至10楼
                    </div>
                </div>
            </div>
            
            <!-- 9月20日-21日 -->
            <div class="timeline-item right milestone">
                <div class="timeline-content milestone">
                    <div class="timeline-date">9月20日-21日</div>
                    <div class="timeline-title">核心设备搬迁</div>
                    <div class="timeline-description">
                        <strong>周末录播期施工：</strong><br>
                        • 音频工作站、调音台<br>
                        • 新媒体直播设备<br>
                        • 播出站（小说）等<br>
                        • 完成全部设备迁移（含语录室、办公区）
                    </div>
                </div>
            </div>
            
            <!-- 9月22日 -->
            <div class="timeline-item left milestone">
                <div class="timeline-content milestone">
                    <div class="timeline-date">9月22日</div>
                    <div class="timeline-title">正式直播启动</div>
                    <div class="timeline-description">
                        <strong>梅江10层演播室首次正式直播</strong><br>
                        技术系统需全程保障
                    </div>
                </div>
            </div>
        </div>
        
        <div class="status-legend">
            <div class="legend-item">
                <div class="legend-color legend-blue"></div>
                <span>常规任务节点</span>
            </div>
            <div class="legend-item">
                <div class="legend-color legend-red"></div>
                <span>重要里程碑</span>
            </div>
        </div>
    </div>
</body>
</html>
