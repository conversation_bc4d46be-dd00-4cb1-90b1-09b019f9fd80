<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文艺广播技术搬迁工作时间轴</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            font-size: 18px;
            line-height: 1.8;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 6px 12px rgba(0,0,0,0.15);
            padding: 50px;
        }
        .header {
            text-align: center;
            margin-bottom: 60px;
        }
        .header h1 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 42px;
            font-weight: bold;
        }
        .header .subtitle {
            color: #7f8c8d;
            font-size: 24px;
            font-weight: 500;
        }
        .timeline {
            position: relative;
            padding: 40px 0;
        }
        .timeline::before {
            content: '';
            position: absolute;
            left: 50%;
            top: 0;
            bottom: 0;
            width: 6px;
            background: linear-gradient(to bottom, #3498db, #e74c3c);
            transform: translateX(-50%);
            border-radius: 3px;
        }
        .timeline-item {
            position: relative;
            margin: 50px 0;
            width: 45%;
        }
        .timeline-item.left {
            left: 0;
            text-align: right;
        }
        .timeline-item.right {
            left: 55%;
            text-align: left;
        }
        .timeline-content {
            background: white;
            border-radius: 12px;
            padding: 35px;
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
            border-left: 6px solid #3498db;
            position: relative;
        }
        .timeline-item.right .timeline-content {
            border-left: none;
            border-right: 6px solid #3498db;
        }
        .timeline-date {
            background: #3498db;
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 20px;
            font-size: 20px;
            box-shadow: 0 3px 8px rgba(52, 152, 219, 0.3);
        }
        .timeline-title {
            font-size: 28px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 20px;
            line-height: 1.4;
        }
        .timeline-description {
            color: #555;
            line-height: 1.8;
            font-size: 18px;
        }
        .timeline-description strong {
            color: #2c3e50;
            font-weight: 600;
        }
        .timeline-description em {
            font-style: normal;
            font-weight: 500;
        }
        .timeline-item::before {
            content: '';
            position: absolute;
            top: 40px;
            width: 24px;
            height: 24px;
            background: #3498db;
            border: 6px solid white;
            border-radius: 50%;
            box-shadow: 0 4px 10px rgba(0,0,0,0.3);
        }
        .timeline-item.left::before {
            right: -66px;
        }
        .timeline-item.right::before {
            left: -66px;
        }
        .milestone {
            border-left-color: #e74c3c !important;
        }
        .timeline-item.right .milestone {
            border-right-color: #e74c3c !important;
        }
        .milestone .timeline-date {
            background: #e74c3c;
            box-shadow: 0 3px 8px rgba(231, 76, 60, 0.3);
        }
        .milestone::before {
            background: #e74c3c !important;
        }
        .status-legend {
            display: flex;
            justify-content: center;
            gap: 50px;
            margin-top: 60px;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 15px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .legend-item {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 20px;
            font-weight: 500;
        }
        .legend-color {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        .legend-blue { background: #3498db; }
        .legend-red { background: #e74c3c; }
        
        @media (max-width: 768px) {
            body {
                font-size: 16px;
                padding: 15px;
            }
            .container {
                padding: 30px 25px;
            }
            .header h1 {
                font-size: 32px;
            }
            .header .subtitle {
                font-size: 20px;
            }
            .timeline::before {
                left: 40px;
            }
            .timeline-item {
                width: calc(100% - 80px);
                left: 80px !important;
                text-align: left !important;
                margin: 40px 0;
            }
            .timeline-item::before {
                left: -52px !important;
            }
            .timeline-content {
                border-left: 6px solid #3498db !important;
                border-right: none !important;
                padding: 25px;
            }
            .timeline-title {
                font-size: 24px;
            }
            .timeline-date {
                font-size: 18px;
                padding: 10px 20px;
            }
            .timeline-description {
                font-size: 16px;
            }
            .status-legend {
                flex-direction: column;
                gap: 20px;
                align-items: center;
            }
            .legend-item {
                font-size: 18px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>文艺广播技术搬迁工作时间轴</h1>
            <div class="subtitle">2024年8月1日-9月31日</div>
        </div>
        
        <div class="timeline">
            <!-- 8月4日前 -->
            <div class="timeline-item left">
                <div class="timeline-content">
                    <div class="timeline-date">8月4日前</div>
                    <div class="timeline-title">装修设计优化完成</div>
                    <div class="timeline-description">
                        完成装修设计优化，提交正式3D效果图（演播室、导播间）
                    </div>
                </div>
            </div>
            
            <!-- 8月10日前 -->
            <div class="timeline-item right">
                <div class="timeline-content">
                    <div class="timeline-date">8月10日前</div>
                    <div class="timeline-title">设计定稿与预算确认</div>
                    <div class="timeline-description">
                        确认最终设计稿，提交预算后启动施工
                    </div>
                </div>
            </div>
            
            <!-- 8月15日-31日 -->
            <div class="timeline-item left">
                <div class="timeline-content">
                    <div class="timeline-date">8月15日-31日</div>
                    <div class="timeline-title">并行施工阶段</div>
                    <div class="timeline-description">
                        <strong>• 布线施工</strong>（10楼机房、5楼、10楼小语录室）<br>
                        <strong>• 演播室机房设备安装调试</strong><br>
                        <em>• 与装修同步进行，节约工期</em>
                    </div>
                </div>
            </div>
            
            <!-- 8月31日前 -->
            <div class="timeline-item right milestone">
                <div class="timeline-content milestone">
                    <div class="timeline-date">8月31日前</div>
                    <div class="timeline-title">🎯 装修施工完成</div>
                    <div class="timeline-description">
                        <strong>完成10层演播室及导播间装修</strong><br>
                        （含灯光、空调修复、门禁调整、安防监控设备安装等）<br>
                        <strong style="color: #e74c3c;">⚠️ 同时完成：3楼备播间改造</strong>
                    </div>
                </div>
            </div>
            
            <!-- 9月1日-20日 -->
            <div class="timeline-item left">
                <div class="timeline-content">
                    <div class="timeline-date">9月1日-20日</div>
                    <div class="timeline-title">基础设备搬迁调试</div>
                    <div class="timeline-description">
                        <strong>• 直播间及导播间基础设备搬迁调试</strong><br>
                        <strong>• 需调通所有路由及信号</strong><br>
                        <em style="color: #3498db;">📅 9月10日前：先行迁移1台音频工作站及1套小语录设备至10楼</em>
                    </div>
                </div>
            </div>
            
            <!-- 9月20日-21日 -->
            <div class="timeline-item right milestone">
                <div class="timeline-content milestone">
                    <div class="timeline-date">9月20日-21日</div>
                    <div class="timeline-title">⚠️ 核心设备搬迁</div>
                    <div class="timeline-description">
                        <strong style="color: #e74c3c; font-size: 20px;">🔧 周末录播期施工：</strong><br>
                        <strong>• 音频工作站、调音台</strong><br>
                        <strong>• 新媒体直播设备</strong><br>
                        <strong>• 播出站（小说广播）等</strong><br>
                        <em style="color: #e74c3c;">• 完成全部设备迁移（含语录室、办公区）</em>
                    </div>
                </div>
            </div>
            
            <!-- 9月22日 -->
            <div class="timeline-item left milestone">
                <div class="timeline-content milestone">
                    <div class="timeline-date">9月22日</div>
                    <div class="timeline-title">🎉 正式直播启动</div>
                    <div class="timeline-description">
                        <strong style="color: #e74c3c; font-size: 22px;">🎯 梅江10层演播室首次正式直播</strong><br>
                        <em style="color: #e74c3c; font-size: 18px;">⚡ 技术系统需全程保障</em>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="status-legend">
            <div class="legend-item">
                <div class="legend-color legend-blue"></div>
                <span>常规任务节点</span>
            </div>
            <div class="legend-item">
                <div class="legend-color legend-red"></div>
                <span>重要里程碑</span>
            </div>
        </div>
    </div>
</body>
</html>
